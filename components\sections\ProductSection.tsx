"use client"

import { useEffect, useRef } from "react"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/dist/ScrollTrigger"

const productsData = [
  {
    title: "产品1 - 企业数字营销全年深度陪跑服务",
    description: "技术+运营长期赋能，打破单次服务局限，企业诊断、基建开发、方案适配、运营落地四大核心服务覆盖数字营销全生命周期，帮助深入分析现状，搭建稳定高效的营销技术底座，同时定制符合企业特点的营销策略并跟进，确保执行效果。",
    features: ["企业诊断", "基建开发", "方案适配", "运营落地"],
    caseStudies: [
      { name: "SCHENDER", growth: "业绩增长300%" },
      { name: "玛缇瓷磗", growth: "转化率提升250%" },
      { name: "其他品牌", growth: "平均增长200%" }
    ],
    bgColor: "bg-techBlue-50",
    accentColor: "text-techBlue-500"
  },
  {
    title: "产品2 - 业务场景数字化系统",
    description: "从工具到增长的全维度赋能。线上线下流量一体化，让顾客\"看见即体验\"。 9大核心系统+深度运营，每个环节都有\"数字引擎\"驱动，团队全程陪跑，从搭建到变现1v1服务，帮您\"把工具用出价值\"。",
    features: ["社群运营", "在线响应", "直播培训", "指导落地", "标杆打造"],
    coreSystems: ["AI+ 智慧门店系统", "AI+ 交付服务系统", "AI+ 积分系统", "异业联盟平台", "派工平台", "供应链平台", "企业内部管理系统", "销售名片", "云商学院"],
    bgColor: "bg-energyOrange-50",
    accentColor: "text-energyOrange-500"
  },
  {
    title: "产品3 - 企业智能体部署及成长",
    description: "基于DeepSeek、星火、百度等主流大模型，注入您的企业知识库（产品数据/服务流程/客户画像），让AI输出\"内部人\"级别的专业回答。根据场景自动匹配最佳模型，问答、报表、客服各司其职，效果提升60%+， 让AI真正\"懂\"你的业务。",
    features: ["模型精调", "API调用集成"],
    highlights: ["工艺合规校验", "实时报价引擎", "智能客服", "智能养护顾问", "工程参数咨询", "智能选品推荐", "营销素材生成", "智能库存调度", "市场需求预测"],
    bgColor: "bg-techBlue-50",
    accentColor: "text-techBlue-500"
  },
  {
    title: "产品4 - 多语言定制建站(国内/海外)",
    description: "通过定制化建站，企业能实现品牌自主化、运营精细化，从视觉到交互，量身打造品牌数字名片，谷歌/Bing/Yandex全球搜索引擎优化，智能排名监控系统，实时调整策略，智能分发至YouTube/TikTok/Facebook等8大平台，AI分析观看数据，精准捕捉高意向客户。",
    features: [],
    channels: ["全定制，重体验", "全球搜索，引擎优化SEO", "全球视频通，VideoGoo"],
    bgColor: "bg-energyOrange-50",
    accentColor: "text-energyOrange-500"
  },
  {
    title: "产品5 - 严选资源，帮企业选择工具少走弯路",
    description: "专业严选认证体系，通过场景预制、集采权益、智能比价三大核心模块，帮助企业跳过繁琐筛选流程，直接获得最优解，并享受专属资源池优惠，从需求诊断→多维评估→防坑指南→资源池共建全流程闭环，确保工具选择\"精准匹配、开放互联、成本可控\"，拒绝被单一厂商绑定，不做工具搬运工。",
    features: ["更多", "更好", "更快", "更省"],
    capabilities: ["AI直播", "AI销售", "AI分析", "AI矩阵", "AI生图", "AI数字人"],
    bgColor: "bg-techBlue-50",
    accentColor: "text-techBlue-500"
  }
]

export default function ProductSection() {
  const sectionRef = useRef<HTMLElement>(null)
  const horizontalScrollRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger)

    // 横向滚动动画
    if (horizontalScrollRef.current && sectionRef.current) {
      const horizontalContainer = horizontalScrollRef.current
      const productCards = horizontalContainer.querySelectorAll('.product-card')

      // 计算总的横向滚动距离 - 每个产品占100vw，所以总距离是(产品数量-1) * 100vw
      const totalWidth = (productsData.length - 1) * window.innerWidth

      // 创建横向滚动动画
      gsap.to(horizontalContainer, {
        x: -totalWidth,
        ease: "none",
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top top",
          end: () => `+=${productsData.length * window.innerHeight}`, // 为每个产品分配一个视窗高度的滚动距离
          scrub: 1,
          pin: true,
          anticipatePin: 1,
          invalidateOnRefresh: true,
          id: "horizontal-scroll"
        }
      })

      // 为每个产品卡片添加进入动画
      productCards.forEach((card) => {
        gsap.fromTo(card,
          {
            opacity: 0,
            y: 100,
            scale: 0.8
          },
          {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 0.8,
            ease: "power2.out",
            scrollTrigger: {
              trigger: card,
              start: "left 80%",
              end: "left 20%",
              toggleActions: "play none none reverse"
            }
          }
        )
      })
    }

    // 简化的淡入动画（用于标题等其他元素）
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fadeInUp")
          }
        })
      },
      { threshold: 0.1 }
    )

    if (sectionRef.current) {
      const elements = sectionRef.current.querySelectorAll(".animate-on-scroll")
      elements.forEach((el) => observer.observe(el))
    }

    return () => {
      observer.disconnect()
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  return (
    <section
      id="products"
      ref={sectionRef}
      className="relative bg-bgPrimary"
    >
      {/* 固定的标题区域 */}
      <div className="py-12 lg:py-16">
        <div className="container mx-auto px-4 lg:px-8">
          <div className="text-center mb-8">
            <h2 className="animate-on-scroll text-4xl lg:text-5xl font-bold text-techBlue-500 mb-4">产品</h2>
            <h3 className="animate-on-scroll text-2xl lg:text-3xl font-bold text-textPrimary mb-6">蜜多云科技全链路产品矩阵</h3>
            <p className="animate-on-scroll text-lg text-textSecondary max-w-3xl mx-auto leading-relaxed">
              我们为大家居企业提供从诊断分析到营销执行的全链路产品解决方案，
              每个产品都经过深度的行业实践验证，确保为您的企业带来实际的增长价值
            </p>
          </div>

          {/* 滚动提示 */}
          <div className="animate-on-scroll text-center mb-6">
            <p className="text-textSecondary text-sm flex items-center justify-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
              向下滚动查看产品详情
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
            </p>
          </div>
        </div>
      </div>

      {/* 横向滚动容器 */}
      <div className="relative min-h-screen overflow-hidden">
        <div
          ref={horizontalScrollRef}
          className="flex h-full items-center"
          style={{ width: `${productsData.length * 100}vw` }}
        >
          {productsData.map((product, index) => (
            <div
              key={index}
              className="product-card flex-shrink-0 w-screen h-full flex items-center justify-center px-4 lg:px-8"
            >
                <div className={`${product.bgColor} rounded-2xl p-6 lg:p-8 border border-divider hover:shadow-hover transition-all duration-300 max-w-5xl w-full max-h-[85vh] overflow-y-auto`}>
                  
                  {/* 产品标题和描述 */}
                  <div className="mb-6">
                    <h3 className={`text-xl lg:text-2xl font-bold ${product.accentColor} mb-3`}>
                      {product.title}
                    </h3>
                    <p className="text-sm lg:text-base text-textSecondary leading-relaxed mb-4">
                      {product.description}
                    </p>
                    
                    {/* 基础特性 */}
                    {product.features.length > 0 && (
                      <div className="flex flex-wrap gap-3">
                        {product.features.map((feature, idx) => (
                          <span 
                            key={idx}
                            className={`px-4 py-2 ${product.accentColor} bg-white rounded-lg text-sm font-medium border border-current`}
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* 产品特色内容 */}
                  <div className="grid lg:grid-cols-2 gap-6">

                    {/* 左侧内容 */}
                    <div className="space-y-4">
                      {/* 客户成功案例 */}
                      {product.caseStudies && (
                        <div>
                          <h4 className="text-base font-bold text-textPrimary mb-3">客户成功案例</h4>
                          <div className="space-y-2">
                            {product.caseStudies.map((caseStudy, idx) => (
                              <div key={idx} className="flex items-center justify-between bg-white p-4 rounded-lg border border-divider">
                                <span className="font-medium text-textPrimary">{caseStudy.name}</span>
                                <span className={`${product.accentColor} font-bold`}>{caseStudy.growth}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* 9大核心系统 */}
                      {product.coreSystems && (
                        <div>
                          <h4 className="text-base font-bold text-textPrimary mb-3">9大核心系统</h4>
                          <div className="grid grid-cols-1 gap-1.5">
                            {product.coreSystems.map((system, idx) => (
                              <div key={idx} className="bg-white p-3 rounded-lg border border-divider">
                                <span className="text-textSecondary text-sm">{system}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* 核心优势 */}
                      {product.highlights && (
                        <div>
                          <h4 className="text-base font-bold text-textPrimary mb-3">核心优势</h4>
                          <div className="grid grid-cols-1 gap-1.5">
                            {product.highlights.map((highlight, idx) => (
                              <div key={idx} className="flex items-center gap-3 bg-white p-3 rounded-lg border border-divider">
                                <svg className={`w-4 h-4 ${product.accentColor} flex-shrink-0`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span className="text-textPrimary text-sm">{highlight}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* 精准引流 */}
                      {product.channels && (
                        <div>
                          <h4 className="text-base font-bold text-textPrimary mb-3">核心能力</h4>
                          <div className="space-y-2">
                            {product.channels.map((channel, idx) => (
                              <div key={idx} className="flex items-center gap-3 bg-white p-4 rounded-lg border border-divider">
                                <svg className={`w-5 h-5 ${product.accentColor} flex-shrink-0`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                                <span className="text-textPrimary font-medium">{channel}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* 核心工具 */}
                      {product.capabilities && (
                        <div>
                          <h4 className="text-lg font-bold text-textPrimary mb-4">核心工具</h4>
                          <div className="grid grid-cols-2 gap-3">
                            {product.capabilities.map((capability, idx) => (
                              <div key={idx} className="bg-white p-3 rounded-lg border border-divider text-center">
                                <span className="text-textSecondary text-sm">{capability}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* 右侧内容 - 统一的产品亮点 */}
                    <div className="bg-white p-4 lg:p-6 rounded-xl border border-divider">
                      <h4 className="text-lg font-bold text-textPrimary mb-3">产品亮点</h4>
                      <ul className="space-y-3">
                        <li className="flex items-start gap-3">
                          <svg className={`w-5 h-5 ${product.accentColor} flex-shrink-0 mt-0.5`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                          </svg>
                          <span className="text-textSecondary text-sm">AI技术驱动，智能化程度高</span>
                        </li>
                        <li className="flex items-start gap-3">
                          <svg className={`w-5 h-5 ${product.accentColor} flex-shrink-0 mt-0.5`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <span className="text-textSecondary text-sm">深度贴合大家居行业需求</span>
                        </li>
                        <li className="flex items-start gap-3">
                          <svg className={`w-5 h-5 ${product.accentColor} flex-shrink-0 mt-0.5`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                          </svg>
                          <span className="text-textSecondary text-sm">数据驱动，效果可量化</span>
                        </li>
                        <li className="flex items-start gap-3">
                          <svg className={`w-5 h-5 ${product.accentColor} flex-shrink-0 mt-0.5`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                          </svg>
                          <span className="text-textSecondary text-sm">专业团队全程服务支持</span>
                        </li>
                      </ul>
                    </div>

                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
    </section>
  )
}
